extends Node

func _ready():
	print("🧪 Rýchly test načítania MainMenu scény...")
	
	# Test načítania scény
	var scene_path = "res://scenes/MainMenu.tscn"
	
	if ResourceLoader.exists(scene_path):
		print("✅ Scéna existuje: " + scene_path)
		
		var scene_resource = load(scene_path)
		if scene_resource:
			print("✅ Scéna načítaná úspešne")
			
			var scene_instance = scene_resource.instantiate()
			if scene_instance:
				print("✅ Scéna instantiated úspešne")
				print("🎉 MainMenu scéna je funkčná!")
			else:
				print("❌ Chyba pri instantiate scény")
		else:
			print("❌ Chyba pri načítaní scény")
	else:
		print("❌ Scéna neexistuje: " + scene_path)
	
	# Test assetov
	print("\n🎨 Test assetov:")
	var assets = [
		"res://assets/generated/menu_background_main.png",
		"res://assets/generated/menu_panel_main.png",
		"res://assets/generated/menu_van_helsing_portrait.png",
		"res://assets/generated/menu_title_frame.png",
		"res://assets/generated/menu_button_nova_hra.png",
		"res://assets/generated/menu_button_kapitoly.png",
		"res://assets/generated/menu_button_nastavenia.png",
		"res://assets/generated/menu_button_o_hre.png",
		"res://assets/generated/menu_decoration_horizontal.png"
	]
	
	var missing = 0
	for asset in assets:
		if ResourceLoader.exists(asset):
			print("✅ " + asset.get_file())
		else:
			print("❌ CHÝBA: " + asset.get_file())
			missing += 1
	
	if missing == 0:
		print("\n🎉 Všetko je pripravené! Môžete spustiť hru.")
	else:
		print("\n⚠️ Chýba " + str(missing) + " assetov")
	
	print("\n🚀 Pre spustenie: F5 alebo Play button v Godot editore")
