#!/usr/bin/env node

import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Načítanie premenných z .env súboru
dotenv.config({ path: './api.env' });

const API_KEY = process.env.SCENARIO_API_KEY;
const API_SECRET = process.env.SCENARIO_API_SECRET;
const BASE_URL = 'https://api.cloud.scenario.com/v1';

// Vytvorenie Basic Auth tokenu
const createAuthToken = (apiKey, apiSecret) => {
    const credentials = `${apiKey}:${apiSecret}`;
    return Buffer.from(credentials).toString('base64');
};

async function listPublicModels() {
    try {
        console.log('🔍 Hľadám verejné modely...\n');
        
        const response = await fetch(`${BASE_URL}/models`, {
            method: 'GET',
            headers: {
                'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            console.error('❌ Chyba pri získavaní modelov:', data);
            return;
        }
        
        console.log(`📋 Našiel som ${data.models.length} modelov:\n`);
        
        data.models.forEach((model, index) => {
            console.log(`${index + 1}. ${model.name}`);
            console.log(`   ID: ${model.id}`);
            console.log(`   Typ: ${model.type}`);
            console.log(`   Status: ${model.status}`);
            console.log(`   Privacy: ${model.privacy}`);
            console.log(`   Capabilities: ${model.capabilities.join(', ')}`);
            if (model.compliantModelIds && model.compliantModelIds.length > 0) {
                console.log(`   Compliant Models: ${model.compliantModelIds.join(', ')}`);
            }
            console.log('');
        });
        
        // Skúsme nájsť verejné modely
        console.log('🔍 Skúšam nájsť verejné modely...\n');
        
        // Testovanie známych verejných modelov
        const publicModels = [
            'flux.1-dev',
            'flux.1-schnell', 
            'stable-diffusion-xl-base-1.0',
            'stable-diffusion-v1-5'
        ];
        
        for (const modelId of publicModels) {
            try {
                const testResponse = await fetch(`${BASE_URL}/generate/txt2img`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        prompt: 'test',
                        modelId: modelId,
                        width: 512,
                        height: 512,
                        numSamples: 1
                    })
                });
                
                const testData = await testResponse.json();
                
                if (testResponse.ok) {
                    console.log(`✅ ${modelId} - FUNGUJE!`);
                } else {
                    console.log(`❌ ${modelId} - ${testData.reason || testData.message}`);
                }
                
            } catch (error) {
                console.log(`💥 ${modelId} - Chyba: ${error.message}`);
            }
        }
        
    } catch (error) {
        console.error('💥 Kritická chyba:', error);
    }
}

listPublicModels();
