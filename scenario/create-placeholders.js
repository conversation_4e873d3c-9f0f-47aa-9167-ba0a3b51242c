#!/usr/bin/env node

import fs from 'fs';
import { createCanvas } from 'canvas';

// Vytvorenie placeholder o<PERSON><PERSON><PERSON><PERSON><PERSON> pre testovanie
const placeholders = [
    {
        name: 'main_menu_background.png',
        width: 720,
        height: 1280,
        color: '#1a0d2e',
        text: '<PERSON>\nMain Menu\nBackground'
    },
    {
        name: 'van_helsing_portrait.png', 
        width: 512,
        height: 512,
        color: '#2d1b3d',
        text: '<PERSON>\nPortrait'
    },
    {
        name: 'gothic_button.png',
        width: 256,
        height: 64,
        color: '#3d2a4a',
        text: 'Gothic Button'
    },
    {
        name: 'title_logo.png',
        width: 512,
        height: 128,
        color: '#4a3757',
        text: 'Prek<PERSON>te Dedičstvo'
    }
];

function createPlaceholder(config) {
    const canvas = createCanvas(config.width, config.height);
    const ctx = canvas.getContext('2d');
    
    // Pozadie
    ctx.fillStyle = config.color;
    ctx.fillRect(0, 0, config.width, config.height);
    
    // Gradient efekt
    const gradient = ctx.createLinearGradient(0, 0, config.width, config.height);
    gradient.addColorStop(0, config.color);
    gradient.addColorStop(1, '#000000');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, config.width, config.height);
    
    // Text
    ctx.fillStyle = '#d4af37'; // Zlatá farba
    ctx.font = `${Math.min(config.width, config.height) / 10}px serif`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    const lines = config.text.split('\n');
    const lineHeight = Math.min(config.width, config.height) / 8;
    const startY = config.height / 2 - (lines.length - 1) * lineHeight / 2;
    
    lines.forEach((line, index) => {
        ctx.fillText(line, config.width / 2, startY + index * lineHeight);
    });
    
    // Rámček
    ctx.strokeStyle = '#d4af37';
    ctx.lineWidth = 4;
    ctx.strokeRect(2, 2, config.width - 4, config.height - 4);
    
    return canvas;
}

// Vytvorenie všetkých placeholder obrázkov
placeholders.forEach(config => {
    console.log(`🎨 Vytváram placeholder: ${config.name}`);
    
    const canvas = createPlaceholder(config);
    const buffer = canvas.toBuffer('image/png');
    
    const outputPath = `assets/generated/${config.name}`;
    fs.writeFileSync(outputPath, buffer);
    
    console.log(`✅ Vytvorený: ${outputPath}`);
});

console.log('\n🎉 Všetky placeholder assety boli vytvorené!');
console.log('📁 Umiestnenie: assets/generated/');
console.log('\n💡 Tieto placeholder obrázky môžete nahradiť skutočnými assetmi z Scenario API');
console.log('   keď budú API kľúče správne nastavené.');
