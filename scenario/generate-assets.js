#!/usr/bin/env node

import dotenv from 'dotenv';
import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';

// Načítanie premenných z .env súboru
dotenv.config({ path: './api.env' });

const API_KEY = process.env.SCENARIO_API_KEY;
const API_SECRET = process.env.SCENARIO_API_SECRET;
const BASE_URL = 'https://api.cloud.scenario.com/v1';

// Vytvorenie Basic Auth tokenu
const createAuthToken = (apiKey, apiSecret) => {
    const credentials = `${apiKey}:${apiSecret}`;
    return Buffer.from(credentials).toString('base64');
};

// Assety na vygenerovanie pre Van Helsing main menu v štýle "Prekliate dedičstvo"
const assetsToGenerate = [
    {
        filename: 'main_menu_background.png',
        prompt: '19th-century Central European gothic castle at night, weathered stone architecture, fog-shrouded environment, deep indigo and cold blue color palette, muted earth tones, flickering candlelight in windows, decaying grandeur, atmospheric mystery, cinematic lighting, desaturated colors, foreboding atmosphere',
        width: 720,
        height: 1280,
        description: 'Pozadie hlavného menu'
    },
    {
        filename: 'van_helsing_portrait.png',
        prompt: 'Van Helsing character portrait, 19th-century Central European style, realistic with subtle grotesque touches, sunken eyes, period-accurate Victorian clothing, finely textured weathered skin, dark coat, determined expression, deep indigo and cold blue lighting, desaturated color palette, cinematic realism',
        width: 512,
        height: 512,
        description: 'Portrét Van Helsinga'
    },
    {
        filename: 'gothic_button.png',
        prompt: 'Medieval ornate UI button frame, distressed parchment texture, brass accents, weathered ancient wood details, gothic ornaments, 19th-century Central European style, muted earth tones, deep indigo highlights, game interface element',
        width: 256,
        height: 128,
        description: 'Gotické tlačidlo'
    },
    {
        filename: 'title_logo.png',
        prompt: 'Gothic text logo "Prekliate Dedičstvo", medieval ornate lettering, distressed parchment background, brass metallic accents, 19th-century Central European typography, deep indigo and muted earth tones, weathered ancient style',
        width: 512,
        height: 128,
        description: 'Logo titulku'
    }
];

async function getAvailableModels() {
    try {
        const response = await fetch(`${BASE_URL}/models`, {
            method: 'GET',
            headers: {
                'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        return data.models;
    } catch (error) {
        console.error('Chyba pri získavaní modelov:', error);
        return [];
    }
}

async function generateImage(prompt, modelId, width = 512, height = 512) {
    try {
        const requestBody = {
            prompt: prompt,
            modelId: modelId,
            width: width,
            height: height,
            numSamples: 1,
            guidance: 7.5,
            numInferenceSteps: 30
        };
        
        console.log(`🎨 Generujem obrázok s promptom: "${prompt.substring(0, 50)}..."`);
        
        const response = await fetch(`${BASE_URL}/generate/txt2img`, {
            method: 'POST',
            headers: {
                'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            console.log(`✅ Generovanie úspešné! Job ID: ${data.job.jobId}`);
            return data.job;
        } else {
            console.error(`❌ Chyba pri generovaní:`, data);
            return null;
        }
        
    } catch (error) {
        console.error('Chyba pri generovaní obrázka:', error);
        return null;
    }
}

async function downloadImage(url, filename) {
    try {
        const response = await fetch(url);
        const buffer = await response.buffer();
        
        const outputPath = path.join('assets', 'generated', filename);
        fs.writeFileSync(outputPath, buffer);
        
        console.log(`💾 Obrázok uložený: ${outputPath}`);
        return true;
    } catch (error) {
        console.error(`Chyba pri sťahovaní ${filename}:`, error);
        return false;
    }
}

async function waitForJob(jobId, maxWaitTime = 120000) {
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
        try {
            const response = await fetch(`${BASE_URL}/jobs/${jobId}`, {
                headers: {
                    'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();

            if (data.job.status === 'success') {
                return data.job;
            } else if (data.job.status === 'failure' || data.job.status === 'canceled') {
                console.error('❌ Generovanie zlyhalo:', data.job);
                return null;
            }

            console.log(`⏳ Čakám na dokončenie... Status: ${data.job.status}`);
            await new Promise(resolve => setTimeout(resolve, 3000));

        } catch (error) {
            console.error('Chyba pri kontrole statusu:', error);
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }

    console.error('⏰ Timeout - generovanie trvá príliš dlho');
    return null;
}

async function generateAllAssets() {
    console.log('🚀 Spúšťam generovanie assetov pre Van Helsing main menu...\n');
    
    // Získanie dostupných modelov
    console.log('📋 Získavam zoznam dostupných modelov...');
    const models = await getAvailableModels();
    
    if (models.length === 0) {
        console.error('❌ Žiadne modely nie sú dostupné');
        return;
    }
    
    console.log(`✅ Našiel som ${models.length} modelov`);
    
    // Použijeme flux.1-dev model (verejný, kvalitný model)
    const selectedModelId = 'flux.1-dev';
    console.log(`🎯 Používam model: ${selectedModelId}\n`);
    
    // Vytvorenie priečinka ak neexistuje
    const outputDir = path.join('assets', 'generated');
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // Generovanie každého assetu
    for (let i = 0; i < assetsToGenerate.length; i++) {
        const asset = assetsToGenerate[i];
        console.log(`\n📸 [${i + 1}/${assetsToGenerate.length}] ${asset.description}`);
        
        // Generovanie obrázka
        const job = await generateImage(
            asset.prompt,
            selectedModelId,
            asset.width,
            asset.height
        );

        if (!job) {
            console.error(`❌ Nepodarilo sa vygenerovať ${asset.filename}`);
            continue;
        }

        // Čakanie na dokončenie
        console.log('⏳ Čakám na dokončenie generovania...');
        const completedJob = await waitForJob(job.jobId);

        if (!completedJob || !completedJob.metadata || !completedJob.metadata.assetIds || completedJob.metadata.assetIds.length === 0) {
            console.error(`❌ Generovanie ${asset.filename} zlyhalo`);
            continue;
        }

        // Získanie URL obrázka z asset ID
        const assetId = completedJob.metadata.assetIds[0];
        const assetResponse = await fetch(`${BASE_URL}/assets/${assetId}`, {
            headers: {
                'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                'Content-Type': 'application/json'
            }
        });

        const assetData = await assetResponse.json();
        const imageUrl = assetData.asset.url;
        const success = await downloadImage(imageUrl, asset.filename);
        
        if (success) {
            console.log(`🎉 ${asset.description} úspešne vytvorený!`);
        }
        
        // Krátka pauza medzi generovaniami
        if (i < assetsToGenerate.length - 1) {
            console.log('⏸️ Krátka pauza...');
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
    
    console.log('\n🎊 Generovanie assetov dokončené!');
    console.log('📁 Všetky assety sú uložené v: assets/generated/');
    console.log('🎮 Môžete teraz spustiť Godot projekt a otestovať main menu!');
}

// Spustenie generovania
generateAllAssets().catch(error => {
    console.error('💥 Kritická chyba:', error);
    process.exit(1);
});
