#!/usr/bin/env node

import dotenv from 'dotenv';
import fetch from 'node-fetch';
import fs from 'fs';

// Načítanie premenných z .env súboru
dotenv.config({ path: './api.env' });

const API_KEY = process.env.SCENARIO_API_KEY;
const API_SECRET = process.env.SCENARIO_API_SECRET;
const BASE_URL = 'https://api.cloud.scenario.com/v1';

// Vytvorenie Basic Auth tokenu
const createAuthToken = (apiKey, apiSecret) => {
    const credentials = `${apiKey}:${apiSecret}`;
    return Buffer.from(credentials).toString('base64');
};

// Tlačidlá s minimálnou výškou 128px
const buttonAssets = [
    {
        filename: 'menu_button_nova_hra.png',
        prompt: 'dark anime gothic UI button with ornate frame, medieval parchment texture, brass metallic accents, weathered ancient wood, deep purple and gold colors, ornamental gothic details, game interface element, rectangular button design',
        width: 320,
        height: 128,
        description: 'Tlačidlo Nová hra'
    },
    {
        filename: 'menu_button_kapitoly.png',
        prompt: 'dark anime gothic UI button with ornate frame, medieval parchment texture, brass metallic accents, weathered ancient wood, deep purple and gold colors, ornamental gothic details, game interface element, rectangular button design',
        width: 320,
        height: 128,
        description: 'Tlačidlo Kapitoly'
    },
    {
        filename: 'menu_button_nastavenia.png',
        prompt: 'dark anime gothic UI button with ornate frame, medieval parchment texture, brass metallic accents, weathered ancient wood, deep purple and gold colors, ornamental gothic details, game interface element, rectangular button design',
        width: 320,
        height: 128,
        description: 'Tlačidlo Nastavenia'
    },
    {
        filename: 'menu_button_o_hre.png',
        prompt: 'dark anime gothic UI button with ornate frame, medieval parchment texture, brass metallic accents, weathered ancient wood, deep purple and gold colors, ornamental gothic details, game interface element, rectangular button design',
        width: 320,
        height: 128,
        description: 'Tlačidlo O hre'
    },
    {
        filename: 'menu_decoration_horizontal.png',
        prompt: 'dark anime gothic ornamental decoration, medieval brass ornament, intricate gothic patterns, weathered metallic texture, deep gold and bronze colors, symmetrical design, horizontal decorative element',
        width: 600,
        height: 128,
        description: 'Horizontálna dekorácia'
    }
];

async function generateAsset(asset) {
    try {
        console.log(`\n🎨 Generujem: ${asset.description}`);
        console.log(`   Súbor: ${asset.filename}`);
        console.log(`   Rozmer: ${asset.width}x${asset.height}`);
        
        const requestBody = {
            prompt: asset.prompt,
            modelId: 'flux.1-dev',
            width: asset.width,
            height: asset.height,
            numSamples: 1,
            guidance: 7.5,
            numInferenceSteps: 30
        };
        
        const response = await fetch(`${BASE_URL}/generate/txt2img`, {
            method: 'POST',
            headers: {
                'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            console.log(`❌ Chyba pri generovaní: ${data.reason || data.message}`);
            return false;
        }
        
        console.log(`✅ Generovanie spustené! Job ID: ${data.job.jobId}`);
        
        // Čakanie na dokončenie
        let completed = false;
        let attempts = 0;
        const maxAttempts = 40;
        
        while (!completed && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 3000));
            attempts++;
            
            const statusResponse = await fetch(`${BASE_URL}/jobs/${data.job.jobId}`, {
                headers: {
                    'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                    'Content-Type': 'application/json'
                }
            });
            
            const statusData = await statusResponse.json();
            
            if (statusData.job.status === 'success') {
                console.log('✅ Generovanie dokončené!');
                
                // Získanie asset URL
                const assetId = statusData.job.metadata.assetIds[0];
                const assetResponse = await fetch(`${BASE_URL}/assets/${assetId}`, {
                    headers: {
                        'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const assetData = await assetResponse.json();
                const imageUrl = assetData.asset.url;
                
                // Stiahnutie obrázka
                const imageResponse = await fetch(imageUrl);
                const buffer = await imageResponse.buffer();
                
                const outputPath = `assets/generated/${asset.filename}`;
                fs.writeFileSync(outputPath, buffer);
                
                console.log(`💾 Asset uložený: ${outputPath}`);
                console.log(`🎉 ${asset.description} - HOTOVO!`);
                completed = true;
                return true;
                
            } else if (statusData.job.status === 'failure' || statusData.job.status === 'canceled') {
                console.log(`❌ Generovanie zlyhalo: ${statusData.job.status}`);
                break;
            } else {
                console.log(`⏳ Status: ${statusData.job.status} (${attempts}/${maxAttempts})`);
            }
        }
        
        if (!completed) {
            console.log('⏰ Timeout - generovanie trvá príliš dlho');
        }
        
        return completed;
        
    } catch (error) {
        console.log(`💥 Chyba: ${error.message}`);
        return false;
    }
}

async function main() {
    console.log('🎮 GENEROVANIE TLAČIDIEL PRE MAIN MENU');
    console.log('🎭 Dark anime strašidelný štýl - opravené rozmery\n');
    
    console.log(`📋 Generujem ${buttonAssets.length} assetov:`);
    buttonAssets.forEach((asset, index) => {
        console.log(`   ${index + 1}. ${asset.description} (${asset.width}x${asset.height})`);
    });
    
    console.log('\n🚀 Začínam generovanie...\n');
    
    let successCount = 0;
    let failCount = 0;
    
    for (let i = 0; i < buttonAssets.length; i++) {
        const asset = buttonAssets[i];
        console.log(`\n📸 [${i + 1}/${buttonAssets.length}] ${asset.description.toUpperCase()}`);
        
        const success = await generateAsset(asset);
        
        if (success) {
            successCount++;
        } else {
            failCount++;
        }
        
        // Pauza medzi generovaniami
        if (i < buttonAssets.length - 1) {
            console.log('⏸️ Krátka pauza pred ďalším assetom...');
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
    
    console.log('\n🎊 GENEROVANIE TLAČIDIEL DOKONČENÉ!');
    console.log(`✅ Úspešne vygenerované: ${successCount} assetov`);
    console.log(`❌ Neúspešné: ${failCount} assetov`);
    console.log(`📁 Všetky assety sú v: assets/generated/`);
    
    if (successCount > 0) {
        console.log('\n🏆 KOMPLETNÝ SET MAIN MENU ASSETOV:');
        console.log('   ✅ Pozadie: menu_background_main.png (720x1280)');
        console.log('   ✅ Panel: menu_panel_main.png (400x600)');
        console.log('   ✅ Portrét: menu_van_helsing_portrait.png (304x400)');
        console.log('   ✅ Rámček: menu_title_frame.png (504x152)');
        console.log('   ✅ Tlačidlá: 4x gotické tlačidlá (320x128)');
        console.log('   ✅ Dekorácia: horizontálny ornament (600x128)');
        
        console.log('\n🎮 IMPLEMENTÁCIA DO GODOT:');
        console.log('   1. Otvorte scenario/scenes/MainMenu.tscn');
        console.log('   2. Nastavte menu_background_main.png ako pozadie');
        console.log('   3. Použite menu_panel_main.png ako hlavný panel');
        console.log('   4. Implementujte tlačidlá s gotickým štýlom');
        console.log('   5. Pridajte Van Helsing portrét a dekorácie');
        console.log('   6. Použite len text pre titulok "Prekliate dedičstvo"');
        
        console.log('\n💡 ŠTÝL: Jednotný dark anime strašidelný vzhľad s gotickými ornamentmi');
    }
}

main().catch(error => {
    console.error('💥 Kritická chyba:', error);
    process.exit(1);
});
