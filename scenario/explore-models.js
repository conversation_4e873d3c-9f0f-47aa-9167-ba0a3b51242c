#!/usr/bin/env node

import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Načítanie premenných z .env súboru
dotenv.config({ path: './api.env' });

const API_KEY = process.env.SCENARIO_API_KEY;
const API_SECRET = process.env.SCENARIO_API_SECRET;
const BASE_URL = 'https://api.cloud.scenario.com/v1';

// Vytvorenie Basic Auth tokenu
const createAuthToken = (apiKey, apiSecret) => {
    const credentials = `${apiKey}:${apiSecret}`;
    return Buffer.from(credentials).toString('base64');
};

async function getModelRecommendations() {
    try {
        console.log('🔍 Získavam odporúčania modelov...\n');
        
        const response = await fetch(`${BASE_URL}/recommendations/models`, {
            method: 'GET',
            headers: {
                'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            console.log(`✅ Našiel som ${data.models ? data.models.length : 0} odporúčaných modelov:\n`);
            
            if (data.models) {
                data.models.forEach((model, index) => {
                    console.log(`${index + 1}. ${model.name || model.id}`);
                    console.log(`   ID: ${model.id}`);
                    if (model.type) console.log(`   Typ: ${model.type}`);
                    if (model.description) console.log(`   Popis: ${model.description}`);
                    if (model.tags) console.log(`   Tagy: ${model.tags.join(', ')}`);
                    if (model.capabilities) console.log(`   Schopnosti: ${model.capabilities.join(', ')}`);
                    console.log('');
                });
            }
        } else {
            console.log('❌ Chyba pri získavaní odporúčaní:', data);
        }
        
    } catch (error) {
        console.error('💥 Chyba pri získavaní odporúčaní:', error.message);
    }
}

async function exploreSpecificModels() {
    console.log('\n🎨 Testovanie špecifických modelov pre dark anime štýl...\n');
    
    // Známe modely, ktoré môžu byť vhodné pre dark anime
    const animeModels = [
        'stable-diffusion-xl-base-1.0',
        'flux.1-dev',
        'flux.1-schnell',
        'stable-diffusion-v1-5',
        'anime-diffusion',
        'waifu-diffusion',
        'anything-v4',
        'counterfeit-v3',
        'dark-sushi-mix',
        'deliberate',
        'dreamshaper'
    ];
    
    for (const modelId of animeModels) {
        try {
            // Skúsime získať popis modelu
            const descResponse = await fetch(`${BASE_URL}/models/${modelId}/description`, {
                headers: {
                    'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (descResponse.ok) {
                const descData = await descResponse.json();
                console.log(`✅ ${modelId}`);
                console.log(`   Popis: ${descData.description || 'Bez popisu'}`);
                
                // Skúsime test generovanie
                const testResponse = await fetch(`${BASE_URL}/generate/txt2img`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        prompt: 'dark anime character, gothic style',
                        modelId: modelId,
                        width: 512,
                        height: 512,
                        numSamples: 1
                    })
                });
                
                if (testResponse.ok) {
                    console.log(`   🎯 DOSTUPNÝ pre generovanie!`);
                } else {
                    const testData = await testResponse.json();
                    console.log(`   ❌ Nedostupný: ${testData.reason || testData.message}`);
                }
                
            } else {
                // Skúsime priamo test generovanie
                const testResponse = await fetch(`${BASE_URL}/generate/txt2img`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        prompt: 'dark anime character, gothic style',
                        modelId: modelId,
                        width: 512,
                        height: 512,
                        numSamples: 1
                    })
                });
                
                if (testResponse.ok) {
                    console.log(`✅ ${modelId} - DOSTUPNÝ pre generovanie!`);
                } else {
                    const testData = await testResponse.json();
                    console.log(`❌ ${modelId} - ${testData.reason || testData.message}`);
                }
            }
            
        } catch (error) {
            console.log(`💥 ${modelId} - Chyba: ${error.message}`);
        }
        
        console.log('');
    }
}

async function getModelDetails(modelId) {
    try {
        console.log(`\n📋 Detaily modelu ${modelId}:`);
        
        const response = await fetch(`${BASE_URL}/models/${modelId}/description`, {
            headers: {
                'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log(`   Popis: ${data.description}`);
            if (data.tags) console.log(`   Tagy: ${data.tags.join(', ')}`);
            if (data.examples) console.log(`   Príklady: ${data.examples.length} obrázkov`);
        } else {
            console.log(`   ❌ Nedostupné detaily`);
        }
        
    } catch (error) {
        console.log(`   💥 Chyba: ${error.message}`);
    }
}

async function main() {
    console.log('🎭 Preskúmanie modelov pre dark anime štýl\n');
    
    // 1. Získanie odporúčaní
    await getModelRecommendations();
    
    // 2. Testovanie špecifických modelov
    await exploreSpecificModels();
    
    // 3. Detaily pre najlepšie modely
    console.log('\n🏆 Získavam detaily pre najlepšie modely...');
    await getModelDetails('flux.1-dev');
    await getModelDetails('stable-diffusion-xl-base-1.0');
    
    console.log('\n💡 Odporúčania pre dark anime štýl:');
    console.log('   • flux.1-dev - Najnovší, vysoká kvalita');
    console.log('   • stable-diffusion-xl-base-1.0 - Univerzálny, dobrý pre anime');
    console.log('   • Hľadajte modely s tagmi: anime, dark, gothic, manga');
}

main().catch(error => {
    console.error('💥 Kritická chyba:', error);
    process.exit(1);
});
