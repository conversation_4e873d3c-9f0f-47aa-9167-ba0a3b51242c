{"name": "simple-concat", "description": "Super-minimalist version of `concat-stream`. Less than 15 lines!", "version": "1.0.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/feross/simple-concat/issues"}, "dependencies": {}, "devDependencies": {"standard": "*", "tape": "^5.0.1"}, "homepage": "https://github.com/feross/simple-concat", "keywords": ["concat", "concat-stream", "concat stream"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/simple-concat.git"}, "scripts": {"test": "standard && tape test/*.js"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}