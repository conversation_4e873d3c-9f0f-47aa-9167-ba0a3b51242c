#!/usr/bin/env godot
# Test script pre overenie main menu

extends SceneTree

func _init():
	print("🧪 Testovanie main menu...")
	
	# Test načítania scény
	var main_menu_scene = load("res://scenes/MainMenu.tscn")
	if main_menu_scene:
		print("✅ MainMenu scéna načítaná úspešne")
		
		# Instantiate scénu
		var main_menu = main_menu_scene.instantiate()
		if main_menu:
			print("✅ MainMenu instantiated úspešne")
			
			# Test assetov
			test_assets()
		else:
			print("❌ Chyba pri instantiate MainMenu")
	else:
		print("❌ Chyba pri načítaní MainMenu scény")
	
	quit()

func test_assets():
	"""Test existencie všetkých assetov"""
	var required_assets = [
		"res://assets/generated/menu_background_main.png",
		"res://assets/generated/menu_panel_main.png", 
		"res://assets/generated/menu_van_helsing_portrait.png",
		"res://assets/generated/menu_title_frame.png",
		"res://assets/generated/menu_button_nova_hra.png",
		"res://assets/generated/menu_button_kapitoly.png",
		"res://assets/generated/menu_button_nastavenia.png",
		"res://assets/generated/menu_button_o_hre.png",
		"res://assets/generated/menu_decoration_horizontal.png"
	]
	
	print("\n📋 Testovanie assetov:")
	var missing_count = 0
	
	for asset_path in required_assets:
		if FileAccess.file_exists(asset_path):
			print("✅ " + asset_path.get_file())
		else:
			print("❌ CHÝBA: " + asset_path.get_file())
			missing_count += 1
	
	if missing_count == 0:
		print("\n🎉 Všetky assety sú k dispozícii!")
		print("🎮 Main menu je pripravené na spustenie!")
	else:
		print("\n⚠️ Chýba " + str(missing_count) + " assetov")
		print("💡 Spustite generovanie assetov")
