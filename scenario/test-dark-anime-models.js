#!/usr/bin/env node

import dotenv from 'dotenv';
import fetch from 'node-fetch';
import fs from 'fs';

// Načítanie premenných z .env súboru
dotenv.config({ path: './api.env' });

const API_KEY = process.env.SCENARIO_API_KEY;
const API_SECRET = process.env.SCENARIO_API_SECRET;
const BASE_URL = 'https://api.cloud.scenario.com/v1';

// Vytvorenie Basic Auth tokenu
const createAuthToken = (apiKey, apiSecret) => {
    const credentials = `${apiKey}:${apiSecret}`;
    return Buffer.from(credentials).toString('base64');
};

// Modely na testovanie pre dark anime
const darkAnimeModels = [
    {
        id: 'GPhtCyTWScOBl9hQDkx0UQ',
        name: 'Little Monster Maker',
        description: '<PERSON><PERSON> štýl s monštrami a kreaturami'
    },
    {
        id: 'EL6WsX9eS0KJOtcd_mumQw', 
        name: 'Fairy Queen',
        description: 'Fantasy character art s realizmom'
    },
    {
        id: 'model_B6irErs5ZDuCxrBootfDRShn',
        name: 'RPG Character Trait Avatars',
        description: 'RPG character art'
    },
    {
        id: 'flux.1-dev',
        name: 'Flux.1-dev',
        description: 'Najnovší univerzálny model'
    }
];

// Dark anime test prompty
const testPrompts = [
    'dark anime vampire hunter, gothic style, Van Helsing inspired, moody lighting',
    'dark anime castle at night, gothic architecture, mysterious atmosphere',
    'dark anime character portrait, sunken eyes, Victorian clothing, supernatural',
    'dark anime UI button, gothic ornaments, medieval style, game interface'
];

async function testModel(model, prompt) {
    try {
        console.log(`🎨 Testovanie: ${model.name}`);
        console.log(`   Prompt: "${prompt.substring(0, 50)}..."`);
        
        const requestBody = {
            prompt: prompt,
            modelId: model.id,
            width: 512,
            height: 512,
            numSamples: 1,
            guidance: 7.5,
            numInferenceSteps: 20
        };
        
        const response = await fetch(`${BASE_URL}/generate/txt2img`, {
            method: 'POST',
            headers: {
                'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            console.log(`   ✅ Úspešne spustené! Job ID: ${data.job.jobId}`);
            return {
                success: true,
                jobId: data.job.jobId,
                model: model
            };
        } else {
            console.log(`   ❌ Chyba: ${data.reason || data.message}`);
            return {
                success: false,
                error: data.reason || data.message,
                model: model
            };
        }
        
    } catch (error) {
        console.log(`   💥 Výnimka: ${error.message}`);
        return {
            success: false,
            error: error.message,
            model: model
        };
    }
}

async function generateDarkAnimeAsset(modelId, prompt, filename) {
    try {
        console.log(`\n🎭 Generujem dark anime asset: ${filename}`);
        console.log(`   Model: ${modelId}`);
        console.log(`   Prompt: ${prompt}`);
        
        const requestBody = {
            prompt: prompt,
            modelId: modelId,
            width: 512,
            height: 512,
            numSamples: 1,
            guidance: 7.5,
            numInferenceSteps: 30
        };
        
        const response = await fetch(`${BASE_URL}/generate/txt2img`, {
            method: 'POST',
            headers: {
                'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            console.log(`❌ Chyba pri generovaní: ${data.reason || data.message}`);
            return false;
        }
        
        console.log(`✅ Generovanie spustené! Job ID: ${data.job.jobId}`);
        
        // Čakanie na dokončenie
        let completed = false;
        let attempts = 0;
        const maxAttempts = 40;
        
        while (!completed && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 3000));
            attempts++;
            
            const statusResponse = await fetch(`${BASE_URL}/jobs/${data.job.jobId}`, {
                headers: {
                    'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                    'Content-Type': 'application/json'
                }
            });
            
            const statusData = await statusResponse.json();
            
            if (statusData.job.status === 'success') {
                console.log('✅ Generovanie dokončené!');
                
                // Získanie asset URL
                const assetId = statusData.job.metadata.assetIds[0];
                const assetResponse = await fetch(`${BASE_URL}/assets/${assetId}`, {
                    headers: {
                        'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const assetData = await assetResponse.json();
                const imageUrl = assetData.asset.url;
                
                // Stiahnutie obrázka
                const imageResponse = await fetch(imageUrl);
                const buffer = await imageResponse.buffer();
                
                const outputPath = `assets/generated/${filename}`;
                fs.writeFileSync(outputPath, buffer);
                
                console.log(`💾 Dark anime asset uložený: ${outputPath}`);
                completed = true;
                return true;
                
            } else if (statusData.job.status === 'failure' || statusData.job.status === 'canceled') {
                console.log(`❌ Generovanie zlyhalo: ${statusData.job.status}`);
                break;
            } else {
                console.log(`⏳ Status: ${statusData.job.status}`);
            }
        }
        
        if (!completed) {
            console.log('⏰ Timeout - generovanie trvá príliš dlho');
        }
        
        return completed;
        
    } catch (error) {
        console.log(`💥 Chyba: ${error.message}`);
        return false;
    }
}

async function main() {
    console.log('🎭 Testovanie modelov pre dark anime štýl\n');
    
    // Test všetkých modelov s prvým promptom
    const testPrompt = testPrompts[0];
    const results = [];
    
    for (const model of darkAnimeModels) {
        const result = await testModel(model, testPrompt);
        results.push(result);
        console.log('');
    }
    
    // Zhrnutie výsledkov
    console.log('\n📊 Zhrnutie testov:');
    const workingModels = results.filter(r => r.success);
    const failedModels = results.filter(r => !r.success);
    
    console.log(`✅ Funkčné modely (${workingModels.length}):`);
    workingModels.forEach(r => {
        console.log(`   • ${r.model.name} (${r.model.id})`);
    });
    
    console.log(`❌ Nefunkčné modely (${failedModels.length}):`);
    failedModels.forEach(r => {
        console.log(`   • ${r.model.name}: ${r.error}`);
    });
    
    // Ak máme funkčné modely, vygenerujme ukážku
    if (workingModels.length > 0) {
        const bestModel = workingModels[0].model;
        console.log(`\n🏆 Generujem ukážku s najlepším modelom: ${bestModel.name}`);
        
        await generateDarkAnimeAsset(
            bestModel.id,
            'dark anime Van Helsing character, gothic vampire hunter, moody atmosphere, detailed portrait, supernatural theme',
            'dark_anime_van_helsing.png'
        );
    }
    
    console.log('\n💡 Odporúčania:');
    console.log('   • Použite Little Monster Maker pre anime postavy s dark témou');
    console.log('   • Flux.1-dev je univerzálny a funguje dobre s anime promptmi');
    console.log('   • Pridajte "dark anime" na začiatok promptov pre lepšie výsledky');
}

main().catch(error => {
    console.error('💥 Kritická chyba:', error);
    process.exit(1);
});
