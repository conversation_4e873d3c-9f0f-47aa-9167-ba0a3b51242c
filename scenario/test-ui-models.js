#!/usr/bin/env node

import dotenv from 'dotenv';
import fetch from 'node-fetch';
import fs from 'fs';

// Načítanie premenných z .env súboru
dotenv.config({ path: './api.env' });

const API_KEY = process.env.SCENARIO_API_KEY;
const API_SECRET = process.env.SCENARIO_API_SECRET;
const BASE_URL = 'https://api.cloud.scenario.com/v1';

// Vytvorenie Basic Auth tokenu
const createAuthToken = (apiKey, apiSecret) => {
    const credentials = `${apiKey}:${apiSecret}`;
    return Buffer.from(credentials).toString('base64');
};

// UI špecializované modely
const uiModels = [
    {
        id: 'Q8g21IvmSvOjOVneyk3O_g',
        name: 'Colorful Digital Icons',
        description: 'Špecializovaný na ikony a UI elementy'
    },
    {
        id: 'model_jcUXudeVvf5FZzNymSUuKatY',
        name: '<PERSON>er Icons',
        description: 'Ikony a props v cartoon štýle'
    },
    {
        id: 'fsITTX3cRnubQKMokR-Fvg',
        name: 'Classic 3D + Striking Icons',
        description: 'Unity kompatibilné ikony s 3D efektmi'
    },
    {
        id: 'flux.1-dev',
        name: 'Flux.1-dev',
        description: 'Univerzálny model (pre porovnanie)'
    }
];

// Dark anime UI test prompty
const uiPrompts = [
    {
        name: 'gothic_menu_button',
        prompt: 'dark anime UI button, gothic ornate frame, medieval style, game interface, dark purple and gold colors, ornamental details',
        width: 256,
        height: 128
    },
    {
        name: 'vampire_icon',
        prompt: 'dark anime vampire icon, gothic style, UI element, game interface, dark red and black colors, fangs symbol',
        width: 128,
        height: 128
    },
    {
        name: 'health_bar_frame',
        prompt: 'dark anime health bar frame, gothic ornate border, UI element, medieval style, dark colors with red accents',
        width: 256,
        height: 64
    },
    {
        name: 'inventory_panel',
        prompt: 'dark anime inventory panel, gothic frame, UI background, medieval parchment style, dark brown and gold colors',
        width: 256,
        height: 256
    }
];

async function testUIModel(model, prompt) {
    try {
        console.log(`🎨 Testovanie UI modelu: ${model.name}`);
        console.log(`   Prompt: "${prompt.prompt.substring(0, 50)}..."`);
        
        const requestBody = {
            prompt: prompt.prompt,
            modelId: model.id,
            width: prompt.width,
            height: prompt.height,
            numSamples: 1,
            guidance: 7.5,
            numInferenceSteps: 20
        };
        
        const response = await fetch(`${BASE_URL}/generate/txt2img`, {
            method: 'POST',
            headers: {
                'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            console.log(`   ✅ Úspešne spustené! Job ID: ${data.job.jobId}`);
            return {
                success: true,
                jobId: data.job.jobId,
                model: model,
                prompt: prompt
            };
        } else {
            console.log(`   ❌ Chyba: ${data.reason || data.message}`);
            return {
                success: false,
                error: data.reason || data.message,
                model: model,
                prompt: prompt
            };
        }
        
    } catch (error) {
        console.log(`   💥 Výnimka: ${error.message}`);
        return {
            success: false,
            error: error.message,
            model: model,
            prompt: prompt
        };
    }
}

async function generateUIAsset(modelId, prompt, filename) {
    try {
        console.log(`\n🎭 Generujem dark anime UI asset: ${filename}`);
        console.log(`   Model: ${modelId}`);
        console.log(`   Rozmer: ${prompt.width}x${prompt.height}`);
        
        const requestBody = {
            prompt: prompt.prompt,
            modelId: modelId,
            width: prompt.width,
            height: prompt.height,
            numSamples: 1,
            guidance: 7.5,
            numInferenceSteps: 30
        };
        
        const response = await fetch(`${BASE_URL}/generate/txt2img`, {
            method: 'POST',
            headers: {
                'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            console.log(`❌ Chyba pri generovaní: ${data.reason || data.message}`);
            return false;
        }
        
        console.log(`✅ Generovanie spustené! Job ID: ${data.job.jobId}`);
        
        // Čakanie na dokončenie
        let completed = false;
        let attempts = 0;
        const maxAttempts = 40;
        
        while (!completed && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 3000));
            attempts++;
            
            const statusResponse = await fetch(`${BASE_URL}/jobs/${data.job.jobId}`, {
                headers: {
                    'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                    'Content-Type': 'application/json'
                }
            });
            
            const statusData = await statusResponse.json();
            
            if (statusData.job.status === 'success') {
                console.log('✅ Generovanie dokončené!');
                
                // Získanie asset URL
                const assetId = statusData.job.metadata.assetIds[0];
                const assetResponse = await fetch(`${BASE_URL}/assets/${assetId}`, {
                    headers: {
                        'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const assetData = await assetResponse.json();
                const imageUrl = assetData.asset.url;
                
                // Stiahnutie obrázka
                const imageResponse = await fetch(imageUrl);
                const buffer = await imageResponse.buffer();
                
                const outputPath = `assets/generated/${filename}`;
                fs.writeFileSync(outputPath, buffer);
                
                console.log(`💾 UI asset uložený: ${outputPath}`);
                completed = true;
                return true;
                
            } else if (statusData.job.status === 'failure' || statusData.job.status === 'canceled') {
                console.log(`❌ Generovanie zlyhalo: ${statusData.job.status}`);
                break;
            } else {
                console.log(`⏳ Status: ${statusData.job.status}`);
            }
        }
        
        if (!completed) {
            console.log('⏰ Timeout - generovanie trvá príliš dlho');
        }
        
        return completed;
        
    } catch (error) {
        console.log(`💥 Chyba: ${error.message}`);
        return false;
    }
}

async function main() {
    console.log('🎨 Testovanie modelov pre dark anime UI elementy\n');
    
    // Test všetkých UI modelov s prvým promptom
    const testPrompt = uiPrompts[0]; // gothic_menu_button
    const results = [];
    
    for (const model of uiModels) {
        const result = await testUIModel(model, testPrompt);
        results.push(result);
        console.log('');
    }
    
    // Zhrnutie výsledkov
    console.log('\n📊 Zhrnutie testov UI modelov:');
    const workingModels = results.filter(r => r.success);
    const failedModels = results.filter(r => !r.success);
    
    console.log(`✅ Funkčné UI modely (${workingModels.length}):`);
    workingModels.forEach(r => {
        console.log(`   • ${r.model.name} (${r.model.id})`);
    });
    
    console.log(`❌ Nefunkčné UI modely (${failedModels.length}):`);
    failedModels.forEach(r => {
        console.log(`   • ${r.model.name}: ${r.error}`);
    });
    
    // Ak máme funkčné modely, vygenerujme kompletný UI set
    if (workingModels.length > 0) {
        const bestModel = workingModels[0].model;
        console.log(`\n🏆 Generujem kompletný UI set s najlepším modelom: ${bestModel.name}`);
        
        for (const prompt of uiPrompts) {
            const filename = `ui_${prompt.name}.png`;
            await generateUIAsset(bestModel.id, prompt, filename);
            
            // Krátka pauza medzi generovaniami
            if (prompt !== uiPrompts[uiPrompts.length - 1]) {
                console.log('⏸️ Krátka pauza...');
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
    }
    
    console.log('\n💡 Odporúčania pre dark anime UI:');
    console.log('   • Colorful Digital Icons - najlepší pre UI elementy');
    console.log('   • Sticker Icons - dobrý pre herné ikony');
    console.log('   • Classic 3D + Striking Icons - pre 3D efekty');
    console.log('   • Pridajte "dark anime UI" na začiatok promptov');
    console.log('   • Použite gothic, medieval, ornate pre dark štýl');
}

main().catch(error => {
    console.error('💥 Kritická chyba:', error);
    process.exit(1);
});
