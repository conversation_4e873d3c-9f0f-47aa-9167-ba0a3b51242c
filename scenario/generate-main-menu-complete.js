#!/usr/bin/env node

import dotenv from 'dotenv';
import fetch from 'node-fetch';
import fs from 'fs';

// Načítanie premenných z .env súboru
dotenv.config({ path: './api.env' });

const API_KEY = process.env.SCENARIO_API_KEY;
const API_SECRET = process.env.SCENARIO_API_SECRET;
const BASE_URL = 'https://api.cloud.scenario.com/v1';

// Vytvorenie Basic Auth tokenu
const createAuthToken = (apiKey, apiSecret) => {
    const credentials = `${apiKey}:${apiSecret}`;
    return Buffer.from(credentials).toString('base64');
};

// Kompletný set assetov pre úžasné main menu "Prekliate dedičstvo"
const mainMenuAssets = [
    {
        filename: 'menu_background_main.png',
        prompt: 'dark anime haunted castle main menu background, 19th-century Central European gothic architecture, weathered stone walls, fog-shrouded towers, flickering candlelight in windows, deep indigo night sky, cold blue moonlight, muted earth tones, atmospheric mystery, cinematic lighting, vertical composition for mobile',
        width: 720,
        height: 1280,
        model: 'flux.1-dev', // Najlepší pre pozadia
        description: 'Hlavné pozadie menu'
    },
    {
        filename: 'menu_button_nova_hra.png',
        prompt: 'dark anime UI button "NOVÁ HRA", gothic ornate frame, medieval parchment texture, brass metallic accents, weathered ancient wood, deep purple and gold colors, ornamental gothic details, game interface element',
        width: 320,
        height: 80,
        model: 'Q8g21IvmSvOjOVneyk3O_g', // Najlepší pre UI
        description: 'Tlačidlo Nová hra'
    },
    {
        filename: 'menu_button_kapitoly.png',
        prompt: 'dark anime UI button "KAPITOLY", gothic ornate frame, medieval parchment texture, brass metallic accents, weathered ancient wood, deep purple and gold colors, ornamental gothic details, game interface element',
        width: 320,
        height: 80,
        model: 'Q8g21IvmSvOjOVneyk3O_g',
        description: 'Tlačidlo Kapitoly'
    },
    {
        filename: 'menu_button_nastavenia.png',
        prompt: 'dark anime UI button "NASTAVENIA", gothic ornate frame, medieval parchment texture, brass metallic accents, weathered ancient wood, deep purple and gold colors, ornamental gothic details, game interface element',
        width: 320,
        height: 80,
        model: 'Q8g21IvmSvOjOVneyk3O_g',
        description: 'Tlačidlo Nastavenia'
    },
    {
        filename: 'menu_button_o_hre.png',
        prompt: 'dark anime UI button "O HRE", gothic ornate frame, medieval parchment texture, brass metallic accents, weathered ancient wood, deep purple and gold colors, ornamental gothic details, game interface element',
        width: 320,
        height: 80,
        model: 'Q8g21IvmSvOjOVneyk3O_g',
        description: 'Tlačidlo O hre'
    },
    {
        filename: 'menu_panel_main.png',
        prompt: 'dark anime main menu panel, gothic ornate frame, medieval parchment background, brass corner decorations, weathered ancient wood borders, deep indigo and gold colors, ornamental gothic details, transparent center for text',
        width: 400,
        height: 600,
        model: 'Q8g21IvmSvOjOVneyk3O_g',
        description: 'Hlavný panel menu'
    },
    {
        filename: 'menu_decoration_top.png',
        prompt: 'dark anime gothic ornamental decoration, medieval brass ornament, intricate gothic patterns, weathered metallic texture, deep gold and bronze colors, symmetrical design, top menu decoration',
        width: 600,
        height: 120,
        model: 'Q8g21IvmSvOjOVneyk3O_g',
        description: 'Horná dekorácia'
    },
    {
        filename: 'menu_decoration_bottom.png',
        prompt: 'dark anime gothic ornamental decoration, medieval brass ornament, intricate gothic patterns, weathered metallic texture, deep gold and bronze colors, symmetrical design, bottom menu decoration',
        width: 600,
        height: 120,
        model: 'Q8g21IvmSvOjOVneyk3O_g',
        description: 'Dolná dekorácia'
    },
    {
        filename: 'menu_van_helsing_portrait.png',
        prompt: 'dark anime Van Helsing character portrait, 19th-century Central European vampire hunter, realistic with subtle grotesque touches, sunken determined eyes, period-accurate Victorian dark coat, finely textured weathered skin, crossbow weapon, deep indigo and cold blue lighting, desaturated color palette, cinematic realism, side profile for menu',
        width: 300,
        height: 400,
        model: 'GPhtCyTWScOBl9hQDkx0UQ', // Najlepší pre anime postavy
        description: 'Portrét Van Helsinga pre menu'
    },
    {
        filename: 'menu_title_frame.png',
        prompt: 'dark anime gothic title frame, ornate medieval border for text, weathered parchment background, brass metallic accents, intricate gothic ornaments, deep purple and gold colors, empty center for title text',
        width: 500,
        height: 150,
        model: 'Q8g21IvmSvOjOVneyk3O_g',
        description: 'Rámček pre titulok'
    }
];

async function generateAsset(asset) {
    try {
        console.log(`\n🎨 Generujem: ${asset.description}`);
        console.log(`   Súbor: ${asset.filename}`);
        console.log(`   Model: ${asset.model}`);
        console.log(`   Rozmer: ${asset.width}x${asset.height}`);
        
        const requestBody = {
            prompt: asset.prompt,
            modelId: asset.model,
            width: asset.width,
            height: asset.height,
            numSamples: 1,
            guidance: 7.5,
            numInferenceSteps: 30
        };
        
        const response = await fetch(`${BASE_URL}/generate/txt2img`, {
            method: 'POST',
            headers: {
                'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            console.log(`❌ Chyba pri generovaní: ${data.reason || data.message}`);
            return false;
        }
        
        console.log(`✅ Generovanie spustené! Job ID: ${data.job.jobId}`);
        
        // Čakanie na dokončenie
        let completed = false;
        let attempts = 0;
        const maxAttempts = 50;
        
        while (!completed && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 3000));
            attempts++;
            
            const statusResponse = await fetch(`${BASE_URL}/jobs/${data.job.jobId}`, {
                headers: {
                    'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                    'Content-Type': 'application/json'
                }
            });
            
            const statusData = await statusResponse.json();
            
            if (statusData.job.status === 'success') {
                console.log('✅ Generovanie dokončené!');
                
                // Získanie asset URL
                const assetId = statusData.job.metadata.assetIds[0];
                const assetResponse = await fetch(`${BASE_URL}/assets/${assetId}`, {
                    headers: {
                        'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET)}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const assetData = await assetResponse.json();
                const imageUrl = assetData.asset.url;
                
                // Stiahnutie obrázka
                const imageResponse = await fetch(imageUrl);
                const buffer = await imageResponse.buffer();
                
                const outputPath = `assets/generated/${asset.filename}`;
                fs.writeFileSync(outputPath, buffer);
                
                console.log(`💾 Asset uložený: ${outputPath}`);
                console.log(`🎉 ${asset.description} - HOTOVO!`);
                completed = true;
                return true;
                
            } else if (statusData.job.status === 'failure' || statusData.job.status === 'canceled') {
                console.log(`❌ Generovanie zlyhalo: ${statusData.job.status}`);
                break;
            } else {
                console.log(`⏳ Status: ${statusData.job.status} (${attempts}/${maxAttempts})`);
            }
        }
        
        if (!completed) {
            console.log('⏰ Timeout - generovanie trvá príliš dlho');
        }
        
        return completed;
        
    } catch (error) {
        console.log(`💥 Chyba: ${error.message}`);
        return false;
    }
}

async function main() {
    console.log('🏰 GENEROVANIE ÚŽASNÉHO MAIN MENU PRE "PREKLIATE DEDIČSTVO"');
    console.log('🎭 Jednotný strašidelný dark anime štýl\n');
    
    console.log(`📋 Plán generovania ${mainMenuAssets.length} assetov:`);
    mainMenuAssets.forEach((asset, index) => {
        console.log(`   ${index + 1}. ${asset.description} (${asset.width}x${asset.height})`);
    });
    
    console.log('\n🚀 Začínam generovanie...\n');
    
    let successCount = 0;
    let failCount = 0;
    
    for (let i = 0; i < mainMenuAssets.length; i++) {
        const asset = mainMenuAssets[i];
        console.log(`\n📸 [${i + 1}/${mainMenuAssets.length}] ${asset.description.toUpperCase()}`);
        
        const success = await generateAsset(asset);
        
        if (success) {
            successCount++;
        } else {
            failCount++;
        }
        
        // Pauza medzi generovaniami
        if (i < mainMenuAssets.length - 1) {
            console.log('⏸️ Krátka pauza pred ďalším assetom...');
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }
    
    console.log('\n🎊 GENEROVANIE MAIN MENU DOKONČENÉ!');
    console.log(`✅ Úspešne vygenerované: ${successCount} assetov`);
    console.log(`❌ Neúspešné: ${failCount} assetov`);
    console.log(`📁 Všetky assety sú v: assets/generated/`);
    
    console.log('\n🎮 ĎALŠIE KROKY:');
    console.log('   1. Otvorte Godot projekt v scenario/ priečinku');
    console.log('   2. Spustite MainMenu.tscn scénu');
    console.log('   3. Implementujte nové assety do UI');
    console.log('   4. Nastavte jednotný strašidelný štýl');
    
    console.log('\n🏆 VYTVORENÉ ASSETY PRE MAIN MENU:');
    console.log('   • Pozadie: Strašidelný hrad v noci');
    console.log('   • Tlačidlá: Gotické s ornamentmi (4x)');
    console.log('   • Panel: Hlavný menu panel');
    console.log('   • Dekorácie: Horná a dolná (2x)');
    console.log('   • Portrét: Van Helsing pre menu');
    console.log('   • Rámček: Pre titulok hry');
    
    console.log('\n💡 ŠTÝL: 19. storočie stredoeurópska gotika, dark anime, strašidelná atmosféra');
}

main().catch(error => {
    console.error('💥 Kritická chyba:', error);
    process.exit(1);
});
