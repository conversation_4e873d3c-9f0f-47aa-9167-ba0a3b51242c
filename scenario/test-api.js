#!/usr/bin/env node

import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Načítanie premenných z .env súboru
dotenv.config({ path: './api.env' });

const API_KEY = process.env.SCENARIO_API_KEY;
const API_SECRET = process.env.SCENARIO_API_SECRET;
const BASE_URL = process.env.SCENARIO_BASE_URL || 'https://api.cloud.scenario.com/v1';

// Vytvorenie Basic Auth tokenu
const createAuthToken = (apiKey, apiSecret) => {
    const credentials = `${apiKey}:${apiSecret}`;
    return Buffer.from(credentials).toString('base64');
};

// Test funkcia
async function testScenarioAPI() {
    console.log('🧪 Testovanie Scenario API pripojenia...\n');
    
    if (!API_KEY) {
        console.error('❌ SCENARIO_API_KEY nie je nastavený');
        return;
    }
    
    console.log('🔑 API Key:', API_KEY.substring(0, 15) + '...');
    console.log('🔐 API Secret:', API_SECRET ? API_SECRET.substring(0, 15) + '...' : 'Nie je nastavený');
    console.log('🌐 Base URL:', BASE_URL);
    
    // Test 1: Získanie zoznamu modelov
    console.log('\n📋 Test 1: Získanie zoznamu modelov...');
    try {
        const response = await fetch(`${BASE_URL}/models`, {
            method: 'GET',
            headers: {
                'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET || '')}`,
                'Content-Type': 'application/json'
            }
        });
        
        console.log('Status:', response.status, response.statusText);
        const data = await response.json();
        console.log('Odpoveď:', JSON.stringify(data, null, 2));
        
    } catch (error) {
        console.error('Chyba:', error.message);
    }
    
    // Test 2: Generovanie obrázka
    console.log('\n🎨 Test 2: Generovanie obrázka...');
    try {
        const requestBody = {
            prompt: 'Gothic haunted castle at night',
            width: 512,
            height: 512,
            numImages: 1,
            guidanceScale: 7.5,
            numInferenceSteps: 20
        };
        
        console.log('Request body:', JSON.stringify(requestBody, null, 2));
        
        const response = await fetch(`${BASE_URL}/generate/txt2img`, {
            method: 'POST',
            headers: {
                'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET || '')}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });
        
        console.log('Status:', response.status, response.statusText);
        const data = await response.json();
        console.log('Odpoveď:', JSON.stringify(data, null, 2));
        
    } catch (error) {
        console.error('Chyba:', error.message);
    }
    
    // Test 3: Alternatívne endpointy
    console.log('\n🔄 Test 3: Testovanie alternatívnych endpointov...');
    
    const endpoints = [
        '/models',
        '/assets',
        '/inferences'
    ];
    
    for (const endpoint of endpoints) {
        try {
            console.log(`\nTestujem: ${endpoint}`);
            const response = await fetch(`${BASE_URL}${endpoint}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Basic ${createAuthToken(API_KEY, API_SECRET || '')}`,
                    'Content-Type': 'application/json'
                }
            });
            
            console.log(`${endpoint} - Status:`, response.status, response.statusText);
            
            if (response.status !== 404) {
                const data = await response.json();
                console.log(`${endpoint} - Odpoveď:`, JSON.stringify(data, null, 2).substring(0, 200) + '...');
            }
            
        } catch (error) {
            console.error(`${endpoint} - Chyba:`, error.message);
        }
    }
}

// Spustenie testu
testScenarioAPI().catch(error => {
    console.error('💥 Kritická chyba:', error);
    process.exit(1);
});
