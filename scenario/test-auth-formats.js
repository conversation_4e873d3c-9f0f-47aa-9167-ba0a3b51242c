#!/usr/bin/env node

import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Načítanie premenných z .env súboru
dotenv.config({ path: './api.env' });

const API_KEY = process.env.SCENARIO_API_KEY;
const API_SECRET = process.env.SCENARIO_API_SECRET;
const BASE_URL = 'https://api.cloud.scenario.com/v1';

console.log('🧪 Testovanie rôznych formátov autentifikácie pre Scenario API...\n');

if (!API_KEY) {
    console.error('❌ SCENARIO_API_KEY nie je nastavený');
    process.exit(1);
}

console.log('🔑 API Key:', API_KEY.substring(0, 15) + '...');
console.log('🔐 API Secret:', API_SECRET ? API_SECRET.substring(0, 15) + '...' : 'Nie je nastaven<PERSON>');

// Test rôznych formátov autentifikácie
const authFormats = [
    {
        name: 'Basic Auth (API_KEY:API_SECRET)',
        headers: {
            'Authorization': `Basic ${Buffer.from(`${API_KEY}:${API_SECRET || ''}`).toString('base64')}`,
            'Content-Type': 'application/json'
        }
    },
    {
        name: 'Bearer Token (API_KEY)',
        headers: {
            'Authorization': `Bearer ${API_KEY}`,
            'Content-Type': 'application/json'
        }
    },
    {
        name: 'API Key Header',
        headers: {
            'X-API-Key': API_KEY,
            'Content-Type': 'application/json'
        }
    },
    {
        name: 'Authorization Header (API_KEY)',
        headers: {
            'Authorization': API_KEY,
            'Content-Type': 'application/json'
        }
    }
];

// Test endpointy
const endpoints = [
    '/models',
    '/inferences', 
    '/assets',
    '/user',
    '/account'
];

async function testAuthFormat(authFormat, endpoint) {
    try {
        console.log(`\n🔍 Testovanie: ${authFormat.name} na ${endpoint}`);
        
        const response = await fetch(`${BASE_URL}${endpoint}`, {
            method: 'GET',
            headers: authFormat.headers
        });
        
        console.log(`   Status: ${response.status} ${response.statusText}`);
        
        if (response.status !== 404) {
            const text = await response.text();
            let data;
            try {
                data = JSON.parse(text);
            } catch {
                data = text.substring(0, 200);
            }
            
            if (response.status === 200) {
                console.log(`   ✅ ÚSPECH! Odpoveď:`, JSON.stringify(data, null, 2).substring(0, 300) + '...');
                return true;
            } else {
                console.log(`   ❌ Chyba:`, JSON.stringify(data, null, 2).substring(0, 200) + '...');
            }
        }
        
    } catch (error) {
        console.log(`   💥 Výnimka:`, error.message);
    }
    
    return false;
}

async function runTests() {
    let successFound = false;
    
    for (const authFormat of authFormats) {
        console.log(`\n🧪 === ${authFormat.name} ===`);
        
        for (const endpoint of endpoints) {
            const success = await testAuthFormat(authFormat, endpoint);
            if (success) {
                successFound = true;
                console.log(`\n🎉 NAŠIEL SOM FUNKČNÝ FORMÁT!`);
                console.log(`   Formát: ${authFormat.name}`);
                console.log(`   Endpoint: ${endpoint}`);
                console.log(`   Headers:`, JSON.stringify(authFormat.headers, null, 2));
                break;
            }
        }
        
        if (successFound) break;
    }
    
    if (!successFound) {
        console.log('\n❌ Žiadny formát autentifikácie nefunguje.');
        console.log('💡 Možné riešenia:');
        console.log('   1. Skontrolujte API kľúče na app.scenario.com');
        console.log('   2. Overte, či sú kľúče aktívne a platné');
        console.log('   3. Skontrolujte, či máte správne povolenia');
        console.log('   4. Možno sa zmenil formát API alebo endpoint');
    }
}

runTests().catch(error => {
    console.error('💥 Kritická chyba:', error);
    process.exit(1);
});
