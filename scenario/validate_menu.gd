extends Control

# Test script pre validáciu main menu

func _ready():
	print("🧪 Validácia main menu...")
	
	# Test UI referencií
	test_ui_references()
	
	# Test assetov
	test_assets()
	
	print("✅ Validácia dokončená!")

func test_ui_references():
	"""Test či sú všetky UI elementy dostupné"""
	print("\n📋 Testovanie UI referencií:")
	
	var ui_elements = {
		"Background": $Background,
		"TitleFrame": $TitleFrame,
		"TitleLabel": $TitleFrame/TitleLabel,
		"VanHelsingPortrait": $VanHelsingPortrait,
		"MainPanel": $MainPanel,
		"ButtonContainer": $MainPanel/ButtonContainer,
		"NovaHraButton": $MainPanel/ButtonContainer/NovaHraButton,
		"KapitolyButton": $MainPanel/ButtonContainer/KapitolyButton,
		"NastaveniaButton": $MainPanel/ButtonContainer/NastaveniaButton,
		"OHreButton": $MainPanel/ButtonContainer/OHreButton,
		"TopDecoration": $TopDecoration,
		"BottomDecoration": $BottomDecoration,
		"StatusLabel": $StatusLabel,
		"ProgressBar": $ProgressBar
	}
	
	var missing_count = 0
	for element_name in ui_elements:
		var element = ui_elements[element_name]
		if element:
			print("✅ " + element_name)
		else:
			print("❌ CHÝBA: " + element_name)
			missing_count += 1
	
	if missing_count == 0:
		print("🎉 Všetky UI elementy sú dostupné!")
	else:
		print("⚠️ Chýba " + str(missing_count) + " UI elementov")

func test_assets():
	"""Test existencie assetov"""
	print("\n🎨 Testovanie assetov:")
	
	var required_assets = [
		"res://assets/generated/menu_background_main.png",
		"res://assets/generated/menu_panel_main.png", 
		"res://assets/generated/menu_van_helsing_portrait.png",
		"res://assets/generated/menu_title_frame.png",
		"res://assets/generated/menu_button_nova_hra.png",
		"res://assets/generated/menu_button_kapitoly.png",
		"res://assets/generated/menu_button_nastavenia.png",
		"res://assets/generated/menu_button_o_hre.png",
		"res://assets/generated/menu_decoration_horizontal.png"
	]
	
	var missing_count = 0
	for asset_path in required_assets:
		if FileAccess.file_exists(asset_path):
			print("✅ " + asset_path.get_file())
		else:
			print("❌ CHÝBA: " + asset_path.get_file())
			missing_count += 1
	
	if missing_count == 0:
		print("🎉 Všetky assety sú k dispozícii!")
	else:
		print("⚠️ Chýba " + str(missing_count) + " assetov")
