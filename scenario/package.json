{"name": "scenario-api-generator", "version": "1.0.0", "description": "Node.js skript pre generovanie ob<PERSON><PERSON><PERSON><PERSON> pomocou Scenario API", "main": "generate-image.js", "type": "module", "scripts": {"start": "node generate-image.js", "test": "node test-api.js"}, "dependencies": {"canvas": "^3.1.2", "dotenv": "^16.3.1", "node-fetch": "^3.3.2"}, "keywords": ["scenario", "api", "image-generation", "ai", "van-he<PERSON>ing"], "author": "Van He<PERSON>ing Project", "license": "MIT"}